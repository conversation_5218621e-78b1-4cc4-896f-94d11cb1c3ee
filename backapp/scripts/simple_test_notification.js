#!/usr/bin/env node

/**
 * Simple test notification script
 * Usage: node scripts/simple_test_notification.js
 */

require('dotenv').config();
const NotificationService = require('../src/services/notificationService');

// Test FCM tokens (these are from your previous successful sends)
const TEST_FCM_TOKENS = [
  // Add some test tokens here - you can get these from your app users
  // For now, we'll try to send to any valid tokens we can find
];

async function sendSimpleTestNotification() {
  try {
    console.log('🧪 Sending simple test notification...');
    
    const notificationService = new NotificationService();
    
    const title = 'Test Notification';
    const body = 'This is a simple test notification from your backend!';
    const imageUrl = 'https://images.unsplash.com/photo-1611262588024-d12430b98920?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80';
    
    const data = {
      type: 'test',
      image: imageUrl,
      message: 'test_notification'
    };

    // If no test tokens provided, we'll need to get them from command line
    const providedTokens = process.argv.slice(2);
    
    if (providedTokens.length === 0 && TEST_FCM_TOKENS.length === 0) {
      console.log('❌ No FCM tokens provided!');
      console.log('');
      console.log('Usage:');
      console.log('node scripts/simple_test_notification.js "YOUR_FCM_TOKEN_HERE"');
      console.log('');
      console.log('Or add multiple tokens:');
      console.log('node scripts/simple_test_notification.js "token1" "token2" "token3"');
      console.log('');
      console.log('💡 You can get FCM tokens from your mobile app users');
      return;
    }
    
    const tokensToUse = providedTokens.length > 0 ? providedTokens : TEST_FCM_TOKENS;
    
    console.log(`📱 Sending to ${tokensToUse.length} FCM token(s)...`);
    
    const results = [];
    
    for (let i = 0; i < tokensToUse.length; i++) {
      const token = tokensToUse[i];
      console.log(`📤 Sending to token ${i + 1}/${tokensToUse.length}: ${token.substring(0, 20)}...`);
      
      const result = await notificationService.sendNotificationToUser(
        token,
        title,
        body,
        data
      );
      
      results.push({
        token: token.substring(0, 20) + '...',
        success: result.success,
        error: result.error,
        messageId: result.messageId
      });
      
      if (result.success) {
        console.log(`✅ Sent successfully! Message ID: ${result.messageId}`);
      } else {
        console.log(`❌ Failed: ${result.error}`);
      }
      
      // Small delay between sends
      if (i < tokensToUse.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    // Summary
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`✅ Successful: ${successful}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((successful / results.length) * 100).toFixed(1)}%`);
    
    if (successful > 0) {
      console.log('\n🎉 Test notification sent successfully!');
      console.log('Check your mobile device for the notification.');
    }
    
  } catch (error) {
    console.error('💥 Error in test notification:', error);
  }
}

// Run the test
sendSimpleTestNotification().catch(console.error);
