#!/usr/bin/env node

/**
 * Simple notification sender script
 * Creates and sends notifications in the database style
 * Usage: node scripts/send_notification.js
 */

const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://yproject85:<EMAIL>/zero_koin';

// Notification Schema (matching your existing schema)
const notificationSchema = new mongoose.Schema({
  image: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  content: {
    type: String,
    default: ''
  },
  link: {
    type: String,
    default: ''
  },
  isSent: {
    type: Boolean,
    default: false
  },
  sentAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

const Notification = mongoose.model('Notification', notificationSchema);

/**
 * Create a notification in the database
 */
async function createNotification(title, content, imageUrl, link = '', markAsSent = false) {
  try {
    console.log('📝 Creating notification in database...');
    
    const notification = new Notification({
      image: imageUrl,
      title: title,
      content: content,
      link: link,
      isSent: markAsSent,
      sentAt: markAsSent ? new Date() : null
    });

    const savedNotification = await notification.save();
    console.log('✅ Notification created successfully!');
    console.log(`   ID: ${savedNotification._id}`);
    console.log(`   Title: ${savedNotification.title}`);
    console.log(`   Content: ${savedNotification.content}`);
    console.log(`   Image: ${savedNotification.image}`);
    console.log(`   Is Sent: ${savedNotification.isSent}`);
    console.log(`   Created At: ${savedNotification.createdAt}`);
    
    return savedNotification;
  } catch (error) {
    console.error('❌ Error creating notification:', error.message);
    return null;
  }
}

/**
 * Get all notifications from database
 */
async function getAllNotifications() {
  try {
    const notifications = await Notification.find().sort({ createdAt: -1 });
    console.log(`📋 Found ${notifications.length} notifications in database:`);
    
    notifications.forEach((notification, index) => {
      console.log(`\n${index + 1}. ${notification.title}`);
      console.log(`   Content: ${notification.content}`);
      console.log(`   Image: ${notification.image}`);
      console.log(`   Sent: ${notification.isSent ? 'Yes' : 'No'}`);
      console.log(`   Created: ${notification.createdAt.toISOString()}`);
    });
    
    return notifications;
  } catch (error) {
    console.error('❌ Error fetching notifications:', error.message);
    return [];
  }
}

/**
 * Main function to handle command line arguments
 */
async function main() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully!');

    const args = process.argv.slice(2);
    const command = args[0];

    if (command === 'list') {
      // List all notifications
      await getAllNotifications();
      
    } else if (command === 'create') {
      // Create a new notification
      const title = args[1];
      const content = args[2];
      const imageUrl = args[3] || 'https://images.unsplash.com/photo-1611262588024-d12430b98920?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80';
      const link = args[4] || '';
      const markAsSent = args[5] === 'true';

      if (!title || !content) {
        console.log('❌ Title and content are required!');
        console.log('');
        console.log('Usage:');
        console.log('node scripts/send_notification.js create "Title" "Content" [imageUrl] [link] [markAsSent]');
        console.log('');
        console.log('Examples:');
        console.log('node scripts/send_notification.js create "Welcome!" "Welcome to ZeroKoin app"');
        console.log('node scripts/send_notification.js create "New Feature" "Check out our new calculator" "https://example.com/image.jpg" "https://app.com" true');
        return;
      }

      await createNotification(title, content, imageUrl, link, markAsSent);
      
    } else {
      // Show help
      console.log('🚀 ZeroKoin Notification Script');
      console.log('================================');
      console.log('');
      console.log('Available commands:');
      console.log('');
      console.log('📋 List all notifications:');
      console.log('   node scripts/send_notification.js list');
      console.log('');
      console.log('📝 Create a new notification:');
      console.log('   node scripts/send_notification.js create "Title" "Content" [imageUrl] [link] [markAsSent]');
      console.log('');
      console.log('Examples:');
      console.log('   node scripts/send_notification.js create "Welcome!" "Welcome to ZeroKoin app"');
      console.log('   node scripts/send_notification.js create "Session Unlocked" "Your session 5 is now available" "https://example.com/image.jpg"');
      console.log('   node scripts/send_notification.js create "Update Available" "New app version is ready" "" "https://app.com" true');
      console.log('');
      console.log('Parameters:');
      console.log('   - Title: Notification title (required)');
      console.log('   - Content: Notification content/body (required)');
      console.log('   - ImageUrl: Image URL for notification (optional, defaults to sample image)');
      console.log('   - Link: Link to open when notification is tapped (optional)');
      console.log('   - MarkAsSent: Set to "true" to mark as sent immediately (optional, defaults to false)');
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed');
  }
}

// Run the script
main().catch(console.error);
